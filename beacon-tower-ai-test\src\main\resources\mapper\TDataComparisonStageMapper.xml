<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.aitest.dao.TDataComparisonStageMapper">

    <!-- 数据对比任务列表DTO结果映射 -->
    <resultMap id="DataComparisonListDTOResultMap" type="com.kf.aitest.dto.DataComparisonListDTO">
        <result column="file_md5" property="fileMd5" jdbcType="VARCHAR"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="success_stage_count" property="successStageCount" jdbcType="INTEGER"/>
        <result column="total_stage_count" property="totalStageCount" jdbcType="INTEGER"/>
        <result column="overall_status" property="overallStatus" jdbcType="VARCHAR"/>
        <result column="overall_score" property="overallScore" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="data_item_count" property="dataItemCount" jdbcType="INTEGER"/>
        <result column="average_duration" property="averageDuration" jdbcType="BIGINT"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 分页查询数据对比任务列表 -->
    <select id="selectTaskList" resultMap="DataComparisonListDTOResultMap">
        SELECT 
            t.file_md5,
            t.task_id,
            t.success_stage_count,
            t.total_stage_count,
            t.data_item_count,
            t.overall_score,
            t.average_duration,
            t.create_time,
            t.update_time,
            t.user_id,
            t.error_message,
            CASE 
                WHEN t.total_stage_count = t.success_stage_count THEN 'SUCCESS'
                WHEN t.success_stage_count > 0 AND t.total_stage_count > t.success_stage_count THEN 'PARTIAL_SUCCESS'
                WHEN t.success_stage_count = 0 AND t.total_stage_count > 0 THEN 'FAILED'
                WHEN t.total_stage_count = 0 THEN 'PENDING'
                ELSE 'RUNNING'
            END as overall_status
        FROM (
            SELECT
                file_md5,
                task_id,
                COUNT(DISTINCT data_id) as data_item_count,
                COUNT(*) as total_stage_count,
                SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_stage_count,
                ROUND(AVG(CASE WHEN ai_score IS NOT NULL THEN ai_score ELSE 0 END)) as overall_score,
                ROUND(AVG(CASE WHEN duration IS NOT NULL THEN duration ELSE 0 END)) as average_duration,
                MIN(create_time) as create_time,
                MAX(update_time) as update_time,
                MIN(user_id) as user_id,
                GROUP_CONCAT(DISTINCT CASE WHEN error_message IS NOT NULL AND error_message != '' THEN error_message END SEPARATOR '; ') as error_message
            FROM t_data_comparison_stage
            WHERE 1=1
                <if test="request.userId != null and request.userId != ''">
                    AND user_id = #{request.userId}
                </if>
                <if test="request.taskId != null and request.taskId != ''">
                    AND task_id = #{request.taskId}
                </if>
                <if test="request.fileMd5 != null and request.fileMd5 != ''">
                    AND file_md5 = #{request.fileMd5}
                </if>
                <if test="request.startTime != null">
                    AND create_time >= #{request.startTime}
                </if>
                <if test="request.endTime != null">
                    AND create_time &lt;= #{request.endTime}
                </if>
                <if test="request.minScore != null">
                    AND ai_score >= #{request.minScore}
                </if>
                <if test="request.maxScore != null">
                    AND ai_score &lt;= #{request.maxScore}
                </if>
                <if test="request.onlyWithErrors != null and request.onlyWithErrors">
                    AND error_message IS NOT NULL AND error_message != ''
                </if>
            GROUP BY task_id, file_md5
        ) t
        WHERE 1=1
            <if test="request.overallStatus != null and request.overallStatus != ''">
                AND (
                    <choose>
                        <when test="request.overallStatus == 'SUCCESS'">
                            t.total_stage_count = t.success_stage_count
                        </when>
                        <when test="request.overallStatus == 'PARTIAL_SUCCESS'">
                            t.success_stage_count > 0 AND t.total_stage_count > t.success_stage_count
                        </when>
                        <when test="request.overallStatus == 'FAILED'">
                            t.success_stage_count = 0 AND t.total_stage_count > 0
                        </when>
                        <when test="request.overallStatus == 'PENDING'">
                            t.total_stage_count = 0
                        </when>
                        <when test="request.overallStatus == 'RUNNING'">
                            t.success_stage_count > 0 AND t.total_stage_count > t.success_stage_count
                        </when>
                    </choose>
                )
            </if>
            <if test="request.onlyCompleted != null and request.onlyCompleted">
                AND (t.total_stage_count = t.success_stage_count OR (t.success_stage_count = 0 AND t.total_stage_count > 0))
            </if>
        ORDER BY 
            <choose>
                <when test="request.orderBy == 'updateTime'">
                    t.update_time
                </when>
                <when test="request.orderBy == 'overallScore'">
                    t.overall_score
                </when>
                <when test="request.orderBy == 'successRate'">
                    (t.success_stage_count * 100.0 / NULLIF(t.total_stage_count, 0))
                </when>
                <otherwise>
                    t.create_time
                </otherwise>
            </choose>
            <choose>
                <when test="request.orderDirection == 'ASC'">
                    ASC
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
    </select>

    <!-- 统计任务总数 -->
    <select id="countTaskList" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT CONCAT(task_id, '-', file_md5))
        FROM t_data_comparison_stage
        WHERE 1=1
            <if test="request.userId != null and request.userId != ''">
                AND user_id = #{request.userId}
            </if>
            <if test="request.taskId != null and request.taskId != ''">
                AND task_id = #{request.taskId}
            </if>
            <if test="request.fileMd5 != null and request.fileMd5 != ''">
                AND file_md5 = #{request.fileMd5}
            </if>
            <if test="request.startTime != null">
                AND create_time >= #{request.startTime}
            </if>
            <if test="request.endTime != null">
                AND create_time &lt;= #{request.endTime}
            </if>
            <if test="request.minScore != null">
                AND ai_score >= #{request.minScore}
            </if>
            <if test="request.maxScore != null">
                AND ai_score &lt;= #{request.maxScore}
            </if>
            <if test="request.onlyWithErrors != null and request.onlyWithErrors">
                AND error_message IS NOT NULL AND error_message != ''
            </if>
    </select>

    <!-- 根据任务ID查询所有阶段结果 -->
    <select id="selectByTaskId" resultType="com.kf.aitest.entity.TDataComparisonStage">
        SELECT * FROM t_data_comparison_stage
        WHERE task_id = #{taskId}
        ORDER BY data_id, stage_name
    </select>

    <!-- 根据任务ID和阶段名称查询阶段结果 -->
    <select id="selectByTaskIdAndStage" resultType="com.kf.aitest.entity.TDataComparisonStage">
        SELECT * FROM t_data_comparison_stage
        WHERE task_id = #{taskId} AND stage_name = #{stageName}
        LIMIT 1
    </select>

    <!-- 根据数据ID查询所有阶段结果 -->
    <select id="selectByDataId" resultType="com.kf.aitest.entity.TDataComparisonStage">
        SELECT * FROM t_data_comparison_stage
        WHERE data_id = #{dataId}
        ORDER BY task_id, stage_name
    </select>

    <!-- 批量插入阶段结果 -->
    <insert id="batchInsert">
        INSERT INTO t_data_comparison_stage (
            task_id, data_id, file_md5, stage_name, data_type, stage_status,
            error_message, duration, fetch_time, ai_evaluation, ai_score,
            uat_data, test_data, create_time, update_time
        ) VALUES
        <foreach collection="stageList" item="stage" separator=",">
            (
                #{stage.taskId}, #{stage.dataId}, #{stage.fileMd5}, #{stage.stageName},
                #{stage.dataType}, #{stage.stageStatus}, #{stage.errorMessage},
                #{stage.duration}, #{stage.fetchTime}, #{stage.aiEvaluation},
                #{stage.aiScore}, #{stage.uatData}, #{stage.testData},
                #{stage.createTime}, #{stage.updateTime}
            )
        </foreach>
    </insert>

    <!-- 统计各阶段的成功率 -->
    <select id="selectStageStatistics" resultType="com.kf.aitest.entity.TDataComparisonStage">
        SELECT
            stage_name,
            COUNT(*) as total_count,
            SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
            ROUND(SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
        FROM t_data_comparison_stage
        WHERE task_id = #{taskId}
        GROUP BY stage_name
        ORDER BY stage_name
    </select>

    <!-- 查询失败的阶段结果 -->
    <select id="selectFailedStages" resultType="com.kf.aitest.entity.TDataComparisonStage">
        SELECT * FROM t_data_comparison_stage
        WHERE task_id = #{taskId} AND stage_status IN ('FAILED', 'TIMEOUT')
        ORDER BY data_id, stage_name
    </select>

</mapper>
