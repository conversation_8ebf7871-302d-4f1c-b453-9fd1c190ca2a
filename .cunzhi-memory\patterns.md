# 常用模式和最佳实践

- AI测试模块一体化重构最佳实践：1)提示词从单文件检查改为双文件对比，忽略页眉页脚差异；2)AI评估服务集成SSE实时推送，支持ai-evaluation-start/complete/error事件；3)设计TDataComparison主表+TDataComparisonStage阶段表的数据库存储方案；4)在ComparisonProgressManager中添加sendAiEvent公开方法；5)修改接口签名传入taskId支持SSE推送。时间戳：2025-07-24T10:37:12+08:00
- AItest前端完全重构最佳实践：1)使用Vue 3 Composition API + TypeScript构建现代化组件架构；2)通过Composables模式(useAITest、useSSEConnection、useMarkdownRenderer)实现逻辑复用；3)集成Pinia Store进行全局状态管理；4)实现Tab页面布局统一管理多个功能模块；5)添加快捷键支持和用户体验优化；6)完善错误处理和状态指示器；7)提供完整的TypeScript类型定义和文档。时间戳：2025-07-29T14:39:19+08:00
- AI测试功能全面改造最佳实践 (2025-08-04T17:26:24+08:00): 
1. 业务流程优化：先保存数据到数据库再进行AI评估，确保数据安全
2. 组件复用策略：充分利用现有CodeDiff.vue组件实现代码对比功能
3. 软删除机制：使用status字段标记删除，保持数据完整性
4. SSE连接智能判断：根据任务状态动态建立实时连接
5. 前端表格重构：按需求精确调整表头字段，优化用户体验
6. MyBatis Plus自动填充：添加MetaObjectHandler修复时间字段问题
- 数据对比列表接口完善最佳实践 (2025-08-05T11:28:03+08:00): 1)使用MyBatis Plus分页插件实现高效分页查询；2)通过GROUP BY task_id, file_md5实现阶段级数据聚合为任务级统计；3)使用CASE WHEN计算整体状态(SUCCESS/PARTIAL_SUCCESS/FAILED/PENDING/RUNNING)；4)采用PaginatedResponse统一分页响应格式；5)实现完整的参数验证(时间范围、评分范围)和异常处理机制；6)提供Swagger API文档注解和完整的单元测试覆盖；7)SQL查询优化：利用现有索引，避免N+1查询问题。
