-- ==================== 数据对比阶段结果表创建脚本 ====================
-- 文件名: create_t_data_comparison_stage.sql
-- 描述: 根据TDataComparisonStage实体类生成的表结构
-- 版本: 1.0
-- 创建时间: 2025-01-05

-- 删除表（如果存在）- 谨慎使用
-- DROP TABLE IF EXISTS t_data_comparison_stage;

-- 创建数据对比阶段结果表
CREATE TABLE t_data_comparison_stage (
    -- ==================== 主键字段 ====================
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
    
    -- ==================== 任务标识字段 ====================
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID，用于SSE连接和任务管理，每次请求生成唯一UUID',
    data_id VARCHAR(64) NOT NULL COMMENT '对比数据ID，标识批量处理中的具体数据项，如用户ID、文档ID等',
    file_md5 VARCHAR(32) DEFAULT NULL COMMENT '请求文件的MD5哈希值，用于去重和缓存，基于请求参数计算',
    
    -- ==================== 阶段信息字段 ====================
    stage_name VARCHAR(32) NOT NULL COMMENT '阶段名称，枚举值：recognize、extraction、structured、transformer',
    data_type VARCHAR(16) DEFAULT NULL COMMENT '数据类型，如：json、md、xml等，便于系统扩展',
    stage_status VARCHAR(32) DEFAULT NULL COMMENT '阶段状态，枚举值：SUCCESS、FAILED、TIMEOUT',
    error_message TEXT DEFAULT NULL COMMENT '错误信息，记录处理失败的详细原因，便于问题定位',
    
    -- ==================== 性能和时间字段 ====================
    duration BIGINT DEFAULT NULL COMMENT '数据获取耗时(毫秒)，用于性能分析和优化',
    fetch_time DATETIME DEFAULT NULL COMMENT '数据获取时间，记录具体的处理时间点',
    
    -- ==================== AI评估相关字段 ====================
    ai_evaluation TEXT DEFAULT NULL COMMENT 'AI评估结果，存储AI分析的详细内容，支持Markdown格式',
    ai_score INT DEFAULT NULL COMMENT 'AI评估得分(0-100)，量化评估结果，便于统计分析',
    
    -- ==================== 数据存储字段 ====================
    uat_data LONGTEXT DEFAULT NULL COMMENT 'UAT环境数据(JSON格式)，存储原始响应数据',
    test_data LONGTEXT DEFAULT NULL COMMENT 'TEST环境数据(JSON格式)，存储原始响应数据',
    
    -- ==================== 审计字段 ====================
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，记录数据插入时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录数据最后修改时间',
    
    -- ==================== 主键定义 ====================
    PRIMARY KEY (id),
    
    -- ==================== 单字段索引 ====================
    INDEX idx_task_id (task_id) COMMENT '任务ID索引，支持按任务查询所有相关数据',
    INDEX idx_data_id (data_id) COMMENT '数据ID索引，支持按数据项查询处理结果',
    INDEX idx_file_md5 (file_md5) COMMENT '文件MD5索引，支持去重查询和缓存机制',
    INDEX idx_stage_name (stage_name) COMMENT '阶段名称索引，支持按阶段统计分析',
    INDEX idx_stage_status (stage_status) COMMENT '阶段状态索引，支持按状态筛选查询',
    INDEX idx_fetch_time (fetch_time) COMMENT '获取时间索引，支持时间范围查询',
    INDEX idx_ai_score (ai_score) COMMENT 'AI评分索引，支持按评分排序和筛选',
    INDEX idx_create_time (create_time) COMMENT '创建时间索引，支持时间排序和分页',
    
    -- ==================== 复合索引 ====================
    INDEX idx_task_data (task_id, data_id) COMMENT '任务-数据复合索引，支持查询任务内特定数据项',
    INDEX idx_task_stage (task_id, stage_name) COMMENT '任务-阶段复合索引，支持任务阶段统计',
    INDEX idx_status_time (stage_status, create_time) COMMENT '状态-时间复合索引，支持状态时间分析',
    INDEX idx_composite_query (task_id, data_id, stage_name) COMMENT '三字段复合索引，支持精确查询和关联',
    
    -- ==================== 唯一约束 ====================
    UNIQUE KEY uk_task_data_stage (task_id, data_id, stage_name) COMMENT '唯一约束：防止同一任务的同一数据项的同一阶段重复记录'
    
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='数据对比阶段结果表，存储每个数据项在各个处理阶段的详细结果，支持批量处理和实时进度跟踪';

-- ==================== 表结构验证 ====================
-- 验证表是否创建成功
-- SHOW CREATE TABLE t_data_comparison_stage;

-- 查看表结构
-- DESCRIBE t_data_comparison_stage;

-- 查看索引信息
-- SHOW INDEX FROM t_data_comparison_stage;
