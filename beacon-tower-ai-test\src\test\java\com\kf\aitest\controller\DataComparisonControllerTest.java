package com.kf.aitest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kf.aitest.dto.DataComparisonListDTO;
import com.kf.aitest.dto.DataComparisonListRequest;
import com.kf.aitest.service.DataComparisonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 数据对比控制器测试
 */
@WebMvcTest(DataComparisonController.class)
public class DataComparisonControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DataComparisonService dataComparisonService;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetTaskList_Success() throws Exception {
        // 准备测试数据
        DataComparisonListDTO dto1 = new DataComparisonListDTO();
        dto1.setFileMd5("test-md5-1");
        dto1.setTaskId("task-1");
        dto1.setSuccessStageCount(3);
        dto1.setTotalStageCount(4);
        dto1.setOverallStatus("PARTIAL_SUCCESS");
        dto1.setOverallScore(85);
        dto1.setCreateTime(LocalDateTime.now());
        dto1.setUserId("user123");

        DataComparisonListDTO dto2 = new DataComparisonListDTO();
        dto2.setFileMd5("test-md5-2");
        dto2.setTaskId("task-2");
        dto2.setSuccessStageCount(4);
        dto2.setTotalStageCount(4);
        dto2.setOverallStatus("SUCCESS");
        dto2.setOverallScore(92);
        dto2.setCreateTime(LocalDateTime.now());
        dto2.setUserId("user123");

        List<DataComparisonListDTO> records = Arrays.asList(dto1, dto2);
        
        // 创建模拟的分页结果
        IPage<DataComparisonListDTO> mockPage = new Page<>(1, 10);
        mockPage.setRecords(records);
        mockPage.setTotal(2);

        // 配置Mock行为
        when(dataComparisonService.getTaskList(any(DataComparisonListRequest.class)))
                .thenReturn(mockPage);

        // 执行测试
        mockMvc.perform(get("/data-comparison/list")
                        .param("current", "1")
                        .param("size", "10")
                        .param("overallStatus", "SUCCESS")
                        .header("userId", "user123")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.isSuccess").value(true))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data.total").value(2))
                .andExpect(jsonPath("$.data.current").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.records").isArray())
                .andExpect(jsonPath("$.data.records[0].fileMd5").value("test-md5-1"))
                .andExpect(jsonPath("$.data.records[0].overallStatus").value("PARTIAL_SUCCESS"))
                .andExpect(jsonPath("$.data.records[1].fileMd5").value("test-md5-2"))
                .andExpect(jsonPath("$.data.records[1].overallStatus").value("SUCCESS"));
    }

    @Test
    void testGetTaskList_WithDefaultParams() throws Exception {
        // 创建空的分页结果
        IPage<DataComparisonListDTO> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Arrays.asList());
        mockPage.setTotal(0);

        // 配置Mock行为
        when(dataComparisonService.getTaskList(any(DataComparisonListRequest.class)))
                .thenReturn(mockPage);

        // 执行测试（不传任何参数，使用默认值）
        mockMvc.perform(get("/data-comparison/list")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.isSuccess").value(true))
                .andExpect(jsonPath("$.data.total").value(0))
                .andExpect(jsonPath("$.data.records").isArray())
                .andExpect(jsonPath("$.data.records").isEmpty());
    }

    @Test
    void testGetTaskList_ServiceException() throws Exception {
        // 配置Mock抛出异常
        when(dataComparisonService.getTaskList(any(DataComparisonListRequest.class)))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试
        mockMvc.perform(get("/data-comparison/list")
                        .param("current", "1")
                        .param("size", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.isSuccess").value(false))
                .andExpect(jsonPath("$.message").value("查询失败: 数据库连接失败"));
    }

    @Test
    void testGetTaskList_InvalidParams() throws Exception {
        // 配置Mock抛出参数异常
        when(dataComparisonService.getTaskList(any(DataComparisonListRequest.class)))
                .thenThrow(new IllegalArgumentException("开始时间不能晚于结束时间"));

        // 执行测试
        mockMvc.perform(get("/data-comparison/list")
                        .param("current", "1")
                        .param("size", "10")
                        .param("startTime", "2025-08-05 12:00:00")
                        .param("endTime", "2025-08-05 10:00:00")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.isSuccess").value(false))
                .andExpect(jsonPath("$.message").value("参数错误: 开始时间不能晚于结束时间"));
    }

    @Test
    void testHealth() throws Exception {
        mockMvc.perform(get("/data-comparison/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.isSuccess").value(true))
                .andExpect(jsonPath("$.message").value("数据对比服务运行正常"));
    }
}
