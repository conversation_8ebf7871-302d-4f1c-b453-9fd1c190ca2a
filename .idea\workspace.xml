<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8982837c-53bc-4b84-9d27-3c99da83e291" name="更改" comment="ai-test">
      <change beforePath="$PROJECT_DIR$/beacon-tower-ai-test/src/main/java/com/kf/aitest/common/PaginatedResponse.java" beforeDir="false" afterPath="$PROJECT_DIR$/beacon-tower-ai-test/src/main/java/com/kf/aitest/common/PaginatedResponse.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/beacon-tower-ai-test/src/main/java/com/kf/aitest/controller/DataComparisonController.java" beforeDir="false" afterPath="$PROJECT_DIR$/beacon-tower-ai-test/src/main/java/com/kf/aitest/controller/DataComparisonController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/beacon-tower-ai-test/src/main/java/com/kf/aitest/dao/TDataComparisonStageMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/beacon-tower-ai-test/src/main/java/com/kf/aitest/dao/TDataComparisonStageMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/beacon-tower-ai-test/src/main/java/com/kf/aitest/service/DataComparisonService.java" beforeDir="false" afterPath="$PROJECT_DIR$/beacon-tower-ai-test/src/main/java/com/kf/aitest/service/DataComparisonService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/beacon-tower-ai-test/src/main/java/com/kf/aitest/service/impl/DataComparisonServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/beacon-tower-ai-test/src/main/java/com/kf/aitest/service/impl/DataComparisonServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../Program Files/maven_repository/com/langfuse/langfuse-java/0.1.0/langfuse-java-0.1.0-sources.jar!/com/langfuse/client/LangfuseClient.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../Program Files/maven_repository/com/langfuse/langfuse-java/0.1.0/langfuse-java-0.1.0-sources.jar!/com/langfuse/client/core/LangfuseClientApiException.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../Program Files/maven_repository/com/langfuse/langfuse-java/0.1.0/langfuse-java-0.1.0-sources.jar!/com/langfuse/client/resources/prompts/PromptsClient.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../Program Files/maven_repository/com/langfuse/langfuse-java/0.1.0/langfuse-java-0.1.0-sources.jar!/com/langfuse/client/resources/prompts/types/PromptMetaListResponse.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Program Files\apache-maven-3.9.9" />
        <option name="localRepository" value="D:\Program Files\maven_repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2wz3ODbIbxRt56eLzkmTp3Lqu3R" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;JUnit.BeaconTowerAiTestApplicationTests.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.BeaconTowerAiTestApplicationTests.testGetSessionInfoFromLangfuse.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.DataFetchServiceTest.testGetStageFileName.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.LangfuseQuickTest.executor&quot;: &quot;Run&quot;,
    &quot;Maven.beacon-tower [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.beacon-tower [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.beacon-tower-ai-test [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.beacon-tower-ai-test [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.beacon-tower-ai-test [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.beacon-tower-bao-si [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.beacon-tower-mcp [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.beacon-tower-mcp [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.beacon-tower-mcp-clint [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.beacon-tower-ui-test [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.beacon-tower-ui-test [compile].executor&quot;: &quot;Run&quot;,
    &quot;Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;找到数据库连接形参&quot;,
    &quot;Notification.DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.BeaconTowerAiTestApplication (1).executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.BeaconTowerAiTestApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.BeaconTowerGatewayApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.BeaconTowerUserServiceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/work/beacon-tower&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDK&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.keymap&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.PlaywrightTest.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.PlaywrightTest2.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\work\beacon-tower\beacon-tower-ai-test\src\main\resources" />
      <recent name="D:\work\beacon-tower\beacon-tower-bao-si\src\main\resources\static" />
      <recent name="D:\work\beacon-tower\beacon-tower-bao-si" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\work\beacon-tower\beacon-tower-ai-test\src\main\resources\static" />
      <recent name="D:\work\beacon-tower\beacon-tower-ai-test\src\test\java\com\kf\aitest\static" />
      <recent name="D:\work\beacon-tower\beacon-tower-bao-si\src\main\resources\static" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="JUnit.BeaconTowerAiTestApplicationTests">
    <configuration name="PlaywrightTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="PlaywrightTest" />
      <module name="beacon-tower-ui-test" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerAiTestApplicationTests" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="beacon-tower-ai-test" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kf.aitest.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.kf.aitest" />
      <option name="MAIN_CLASS_NAME" value="com.kf.aitest.BeaconTowerAiTestApplicationTests" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerAiTestApplicationTests.testGetSessionInfoFromLangfuse" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="beacon-tower-ai-test" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kf.aitest.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.kf.aitest" />
      <option name="MAIN_CLASS_NAME" value="com.kf.aitest.BeaconTowerAiTestApplicationTests" />
      <option name="METHOD_NAME" value="testGetSessionInfoFromLangfuse" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DataFetchServiceTest.testGetStageFileName" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="beacon-tower-ai-test" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kf.aitest.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.kf.aitest.service" />
      <option name="MAIN_CLASS_NAME" value="com.kf.aitest.service.DataFetchServiceTest" />
      <option name="METHOD_NAME" value="testGetStageFileName" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LangfuseQuickTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="beacon-tower-ai-test" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kf.aitest.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.kf.aitest" />
      <option name="MAIN_CLASS_NAME" value="com.kf.aitest.LangfuseQuickTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerAccurateTestApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="beacon-tower-accurate-test" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kf.accuratetest.BeaconTowerAccurateTestApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerAiTestApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="beacon-tower-ai-test" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kf.aitest.BeaconTowerAiTestApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerApiTestApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="beacon-tower-api-test" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kf.apitest.BeaconTowerApiTestApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerBaoSiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="beacon-tower-bao-si" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kf.baosi.BeaconTowerBaoSiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerFlowPlaybackApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="beacon-tower-flow-playback" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kf.flowplayback.BeaconTowerFlowPlaybackApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="beacon-tower-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kf.gateway.BeaconTowerGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerMcpApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="beacon-tower-mcp" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kf.mcp.BeaconTowerMcpApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerMcpClintApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="beacon-tower-mcp-clint" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kf.mcpclint.BeaconTowerMcpClintApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerUiTestApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="beacon-tower-ui-test" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kf.uitest.BeaconTowerUiTestApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BeaconTowerUserServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="beacon-tower-user-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kf.userservice.BeaconTowerUserServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="JUnit.DataFetchServiceTest.testGetStageFileName" />
      <item itemvalue="JUnit.BeaconTowerAiTestApplicationTests" />
      <item itemvalue="JUnit.BeaconTowerAiTestApplicationTests.testGetSessionInfoFromLangfuse" />
      <item itemvalue="JUnit.LangfuseQuickTest" />
      <item itemvalue="Spring Boot.BeaconTowerAccurateTestApplication" />
      <item itemvalue="Spring Boot.BeaconTowerAiTestApplication" />
      <item itemvalue="Spring Boot.BeaconTowerApiTestApplication" />
      <item itemvalue="Spring Boot.BeaconTowerBaoSiApplication" />
      <item itemvalue="Spring Boot.BeaconTowerFlowPlaybackApplication" />
      <item itemvalue="Spring Boot.BeaconTowerGatewayApplication" />
      <item itemvalue="Spring Boot.BeaconTowerMcpApplication" />
      <item itemvalue="Spring Boot.BeaconTowerMcpClintApplication" />
      <item itemvalue="Spring Boot.BeaconTowerUiTestApplication" />
      <item itemvalue="Spring Boot.BeaconTowerUserServiceApplication" />
      <item itemvalue="应用程序.PlaywrightTest" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.BeaconTowerAiTestApplicationTests" />
        <item itemvalue="JUnit.DataFetchServiceTest.testGetStageFileName" />
        <item itemvalue="JUnit.BeaconTowerAiTestApplicationTests.testGetSessionInfoFromLangfuse" />
        <item itemvalue="JUnit.LangfuseQuickTest" />
        <item itemvalue="应用程序.PlaywrightTest" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="8982837c-53bc-4b84-9d27-3c99da83e291" name="更改" comment="" />
      <created>1747030541107</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747030541107</updated>
      <workItem from="1747030542265" duration="5059000" />
      <workItem from="1747125987290" duration="1143000" />
      <workItem from="1747185142621" duration="2658000" />
      <workItem from="1747209359743" duration="34901000" />
      <workItem from="1747642059508" duration="37582000" />
      <workItem from="1748242176234" duration="25607000" />
      <workItem from="1748424651636" duration="986000" />
      <workItem from="1748483243193" duration="16977000" />
      <workItem from="1749024790225" duration="23787000" />
      <workItem from="1749200494489" duration="3578000" />
      <workItem from="1749436033290" duration="2875000" />
      <workItem from="1749448413631" duration="19247000" />
      <workItem from="1749623735253" duration="128579000" />
      <workItem from="1750917503002" duration="12995000" />
      <workItem from="1751271539833" duration="1713000" />
      <workItem from="1751276570231" duration="1456000" />
      <workItem from="1751334422624" duration="104535000" />
      <workItem from="1752463616675" duration="45346000" />
      <workItem from="1752719795775" duration="7237000" />
      <workItem from="1752731843864" duration="893000" />
      <workItem from="1752732829164" duration="5402000" />
      <workItem from="1753061837519" duration="28000" />
      <workItem from="1753061878208" duration="102141000" />
      <workItem from="1753669584997" duration="308000" />
      <workItem from="1753669904763" duration="272000" />
      <workItem from="1753670189929" duration="9754000" />
      <workItem from="1753768874493" duration="8855000" />
      <workItem from="1754029267497" duration="846000" />
      <workItem from="1754289118786" duration="12157000" />
    </task>
    <task id="LOCAL-00001" summary="ai-test">
      <option name="closed" value="true" />
      <created>1751771387240</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751771387240</updated>
    </task>
    <task id="LOCAL-00002" summary="ai-test">
      <option name="closed" value="true" />
      <created>1752476659161</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752476659161</updated>
    </task>
    <task id="LOCAL-00003" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753081693423</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753081693423</updated>
    </task>
    <task id="LOCAL-00004" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753148306202</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753148306202</updated>
    </task>
    <task id="LOCAL-00005" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753234232916</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753234232916</updated>
    </task>
    <task id="LOCAL-00006" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753239439750</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753239439750</updated>
    </task>
    <task id="LOCAL-00007" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753239467379</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753239467379</updated>
    </task>
    <task id="LOCAL-00008" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753249941735</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753249941735</updated>
    </task>
    <task id="LOCAL-00009" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753251766116</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753251766116</updated>
    </task>
    <task id="LOCAL-00010" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753251904786</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753251904786</updated>
    </task>
    <task id="LOCAL-00011" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753253558754</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753253558754</updated>
    </task>
    <task id="LOCAL-00012" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753258426530</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753258426530</updated>
    </task>
    <task id="LOCAL-00013" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753262424880</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753262424880</updated>
    </task>
    <task id="LOCAL-00014" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753321760075</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753321760075</updated>
    </task>
    <task id="LOCAL-00015" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753329538632</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753329538632</updated>
    </task>
    <task id="LOCAL-00016" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753334489637</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753334489637</updated>
    </task>
    <task id="LOCAL-00017" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753339390696</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1753339390696</updated>
    </task>
    <task id="LOCAL-00018" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753351830050</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1753351830050</updated>
    </task>
    <task id="LOCAL-00019" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753406688689</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1753406688689</updated>
    </task>
    <task id="LOCAL-00020" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753413479510</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1753413479510</updated>
    </task>
    <task id="LOCAL-00021" summary="ai-test">
      <option name="closed" value="true" />
      <created>1753414450469</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753414450470</updated>
    </task>
    <task id="LOCAL-00022" summary="ai-test">
      <option name="closed" value="true" />
      <created>1754301353575</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1754301353575</updated>
    </task>
    <task id="LOCAL-00023" summary="ai-test">
      <option name="closed" value="true" />
      <created>1754362770374</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1754362770374</updated>
    </task>
    <task id="LOCAL-00024" summary="ai-test">
      <option name="closed" value="true" />
      <created>1754362791073</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1754362791073</updated>
    </task>
    <option name="localTasksCounter" value="25" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-context" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:javax.validation:validation-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
    <option featureType="dependencySupport" implementationName="java:junit:junit" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="executable:docker" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="ai-test" />
    <option name="LAST_COMMIT_MESSAGE" value="ai-test" />
  </component>
</project>