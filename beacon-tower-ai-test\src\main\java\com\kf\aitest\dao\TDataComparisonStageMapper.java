package com.kf.aitest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kf.aitest.dto.DataComparisonListDTO;
import com.kf.aitest.dto.DataComparisonListRequest;
import com.kf.aitest.entity.TDataComparisonStage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据对比阶段结果表Mapper接口
 */
@Mapper
public interface TDataComparisonStageMapper extends BaseMapper<TDataComparisonStage> {

    /**
     * 根据任务ID查询所有阶段结果
     *
     * @param taskId 任务ID
     * @return 阶段结果列表
     */
    List<TDataComparisonStage> selectByTaskId(@Param("taskId") String taskId);

    /**
     * 根据任务ID和阶段名称查询阶段结果
     *
     * @param taskId 任务ID
     * @param stageName 阶段名称
     * @return 阶段结果
     */
    TDataComparisonStage selectByTaskIdAndStage(
            @Param("taskId") String taskId,
            @Param("stageName") String stageName
    );

    /**
     * 根据数据ID查询所有阶段结果
     *
     * @param dataId 数据ID
     * @return 阶段结果列表
     */
    List<TDataComparisonStage> selectByDataId(@Param("dataId") String dataId);

    /**
     * 批量插入阶段结果
     *
     * @param stageList 阶段结果列表
     * @return 插入数量
     */
    int batchInsert(@Param("stageList") List<TDataComparisonStage> stageList);

    /**
     * 统计各阶段的成功率
     *
     * @param taskId 任务ID
     * @return 统计结果
     */
    List<TDataComparisonStage> selectStageStatistics(@Param("taskId") String taskId);

    /**
     * 查询失败的阶段结果
     *
     * @param taskId 任务ID
     * @return 失败的阶段结果列表
     */
    List<TDataComparisonStage> selectFailedStages(@Param("taskId") String taskId);

    /**
     * 分页查询数据对比任务列表（聚合查询）
     * 将阶段级别的数据聚合为任务级别的统计信息
     *
     * @param request 查询请求参数
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<DataComparisonListDTO> selectTaskList(
            @Param("request") DataComparisonListRequest request,
            @Param("page") Page<?> page
    );

    /**
     * 统计任务总数（用于分页）
     *
     * @param request 查询请求参数
     * @return 总数
     */
    Long countTaskList(@Param("request") DataComparisonListRequest request);
}
