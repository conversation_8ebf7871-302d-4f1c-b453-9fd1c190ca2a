package com.kf.aitest.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kf.aitest.dto.DataComparisonListDTO;
import com.kf.aitest.dto.DataComparisonListRequest;
import com.kf.aitest.dto.DataComparisonRequestDTO;
import com.kf.aitest.dto.DataComparisonResultDTO;
import com.kf.aitest.dto.StageDataDTO;
import com.kf.aitest.service.AiEvaluationService;
import com.kf.aitest.service.ComparisonProgressManager;
import com.kf.aitest.entity.TDataComparisonStage;
import com.kf.aitest.service.DataComparisonService;
import com.kf.aitest.service.DataFetchService;
import com.kf.aitest.service.ResultPrintService;
import com.kf.aitest.dao.TDataComparisonStageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.nio.charset.StandardCharsets;

/**
 * 数据对比服务实现
 */
@Slf4j
@Service
public class DataComparisonServiceImpl implements DataComparisonService {
    
    @Autowired
    private DataFetchService dataFetchService;

    @Autowired
    private AiEvaluationService aiEvaluationService;

    @Autowired
    private ComparisonProgressManager progressManager;

    @Autowired
    private ResultPrintService resultPrintService;

    @Autowired
    private TDataComparisonStageMapper dataComparisonStageMapper;

    @Value("${data.comparison.uat.url:https://copilot-uat.pharmaronclinical.com}")
    private String defaultUatUrl;
    
    @Value("${data.comparison.test.url:https://copilot-test.pharmaronclinical.com}")
    private String defaultTestUrl;
    
    // 数据处理的四个阶段，按顺序执行
    private static final List<String> DEFAULT_STAGES = Arrays.asList("recognize", "extraction", "structured", "transformer");
    
    @Override
    public SseEmitter createSseConnection(String taskId) {
        return progressManager.createEmitter(taskId);
    }
    
    @Override
    @Async
    public void startComparison(String taskId, DataComparisonRequestDTO request) {
        log.info("开始数据对比任务: taskId={}, ids={}", taskId, request.getIds());

        // 计算请求数据的MD5哈希值
        String fileMd5 = calculateRequestMd5(request);
        log.info("计算请求MD5: taskId={}, fileMd5={}", taskId, fileMd5);

        // 打印任务开始信息
        resultPrintService.printTaskStart(taskId, request.getIds().size());

        try {
            // 初始化进度
            progressManager.initProgress(taskId, request.getIds().size());
            
            // 获取环境URL
            String uatUrl = request.getUatBaseUrl() != null ? request.getUatBaseUrl() : getDefaultUatUrl();
            String testUrl = request.getTestBaseUrl() != null ? request.getTestBaseUrl() : getDefaultTestUrl();
            
            // 创建并发控制信号量
            Semaphore semaphore = new Semaphore(request.getConcurrentLimit());
            
            // 处理每个ID
            List<CompletableFuture<DataComparisonResultDTO>> futures = new ArrayList<>();
            
            for (int i = 0; i < request.getIds().size(); i++) {
                String id = request.getIds().get(i);
                int idIndex = i;
                
                CompletableFuture<DataComparisonResultDTO> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        semaphore.acquire();
                        return processId(taskId, id, idIndex, uatUrl, testUrl, request, fileMd5);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("任务被中断: taskId={}, id={}", taskId, id);
                        progressManager.sendError(taskId, "任务被中断: " + id);
                        throw new RuntimeException("任务被中断", e);
                    } catch (Exception e) {
                        log.error("处理ID异常: taskId={}, id={}, error={}", taskId, id, e.getMessage(), e);
                        progressManager.sendError(taskId, "处理ID失败: " + id + ", " + e.getMessage());
                        throw e;
                    } finally {
                        semaphore.release();
                    }
                });
                
                futures.add(future);
            }
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            // 完成任务
            progressManager.completeTask(taskId);
            log.info("数据对比任务完成: taskId={}", taskId);

            // 打印任务完成信息
            resultPrintService.printTaskComplete(taskId);
            
        } catch (Exception e) {
            log.error("数据对比任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            String errorMessage = "任务执行失败: " + e.getMessage();
            progressManager.sendError(taskId, errorMessage);
        }
    }
    
    /**
     * 处理单个ID的数据对比
     */
    private DataComparisonResultDTO processId(String taskId, String id, int idIndex,
                                            String uatUrl, String testUrl, DataComparisonRequestDTO request, String fileMd5) {
        log.info("开始处理ID: taskId={}, id={}, index={}, fileMd5={}", taskId, id, idIndex, fileMd5);

        DataComparisonResultDTO result = new DataComparisonResultDTO();
        result.setId(id);
        result.setTaskId(taskId);
        result.setFileMd5(fileMd5);
        result.setStartTime(LocalDateTime.now());
        result.setStageResults(new ArrayList<>());
        result.setErrors(new ArrayList<>());

        // 更新当前处理的ID
        progressManager.updateCurrentId(taskId, id, idIndex);

        int successCount = 0;

        // 确定要处理的阶段列表
        List<String> stagesToProcess = request.getStages() != null && !request.getStages().isEmpty()
                ? request.getStages()
                : DEFAULT_STAGES;

        log.info("处理阶段列表: {}", stagesToProcess);

        try {
            // 按顺序处理指定的阶段
            for (int stageIndex = 0; stageIndex < stagesToProcess.size(); stageIndex++) {
                String stageName = stagesToProcess.get(stageIndex);

                // 验证阶段名称是否有效
                if (!DEFAULT_STAGES.contains(stageName)) {
                    log.warn("无效的阶段名称: {}, 跳过处理", stageName);
                    continue;
                }

                try {
                    // 打印阶段开始信息
                    resultPrintService.printStageSeparator(stageName, id);

                    // 更新当前阶段
                    progressManager.updateCurrentStage(taskId, stageName, stageIndex);

                    // 获取阶段数据
                    StageDataDTO stageData = dataFetchService.fetchStageData(
                            id, stageName, uatUrl, testUrl, request.getTimeoutSeconds());

                    // 设置fileMd5到阶段数据中
                    stageData.setFileMd5(fileMd5);

                    // 保存阶段数据到数据库（如果有用户ID）
                    Long stageRecordId = null;
                    if (request.getUserId() != null && "SUCCESS".equals(stageData.getStatus())) {
                        try {
                            // 直接保存阶段数据到数据库
                            TDataComparisonStage stage = new TDataComparisonStage();
                            stage.setTaskId(taskId);
                            stage.setDataId(id);
                            stage.setFileMd5(fileMd5);
                            stage.setStageName(stageData.getStageName());
                            stage.setDataType(stageData.getDataType());
                            stage.setStageStatus(stageData.getStatus());
                            stage.setErrorMessage(stageData.getErrorMessage());
                            stage.setDuration(stageData.getDuration());
                            stage.setFetchTime(stageData.getFetchTime());

                            dataComparisonStageMapper.insert(stage);
                            stageRecordId = stage.getId();

                            log.info("阶段数据已保存到数据库: taskId={}, stage={}, recordId={}",
                                    taskId, stageName, stageRecordId);
                        } catch (Exception e) {
                            log.error("保存阶段数据失败: taskId={}, stage={}, error={}",
                                    taskId, stageName, e.getMessage(), e);
                        }
                    }

                    // 再进行AI评估（如果启用且数据获取成功）
                    if (request.getEnableAiEvaluation() && "SUCCESS".equals(stageData.getStatus())) {
                        try {
                            // 检查是否为AiEvaluationServiceImpl实例，支持禁用分片
                            if (aiEvaluationService instanceof com.kf.aitest.service.impl.AiEvaluationServiceImpl) {
                                com.kf.aitest.service.impl.AiEvaluationServiceImpl impl =
                                    (com.kf.aitest.service.impl.AiEvaluationServiceImpl) aiEvaluationService;
                                stageData = impl.evaluateStageData(taskId, stageData,
                                    request.getDisableChunking() != null ? request.getDisableChunking() : false);
                            } else {
                                stageData = aiEvaluationService.evaluateStageData(taskId, stageData);
                            }

                            // 更新数据库中的AI评估结果
                            if (stageRecordId != null) {
                                try {
                                    TDataComparisonStage updateStage = new TDataComparisonStage();
                                    updateStage.setId(stageRecordId);
                                    updateStage.setAiEvaluation(stageData.getAiEvaluation());
                                    updateStage.setAiScore(stageData.getAiScore());
                                    dataComparisonStageMapper.updateById(updateStage);
                                } catch (Exception e) {
                                    log.error("更新AI评估结果失败: stageRecordId={}, error={}", stageRecordId, e.getMessage());
                                }
                            }
                        } catch (Exception e) {
                            log.error("AI评估失败: taskId={}, stage={}, error={}",
                                    taskId, stageName, e.getMessage(), e);
                            stageData.setErrorMessage("AI评估失败: " + e.getMessage());
                        }
                    }

                    result.getStageResults().add(stageData);

                    // 打印阶段结果
                    resultPrintService.printStageResult(stageData, id);

                    if ("SUCCESS".equals(stageData.getStatus())) {
                        successCount++;
                    } else {
                        result.getErrors().add(String.format("阶段%s失败: %s", stageName, stageData.getErrorMessage()));
                    }

                    // 完成阶段
                    progressManager.completeStage(taskId, stageName);

                } catch (Exception e) {
                    log.error("处理阶段失败: id={}, stage={}, error={}", id, stageName, e.getMessage(), e);

                    StageDataDTO errorStage = new StageDataDTO();
                    errorStage.setStageName(stageName);
                    errorStage.setStatus("FAILED");
                    errorStage.setErrorMessage(e.getMessage());
                    errorStage.setFetchTime(LocalDateTime.now());

                    result.getStageResults().add(errorStage);
                    result.getErrors().add(String.format("阶段%s异常: %s", stageName, e.getMessage()));
                }
            }

            // 设置结果状态
            result.setSuccessStageCount(successCount);
            result.setTotalStageCount(stagesToProcess.size());
            if (successCount == stagesToProcess.size()) {
                result.setOverallStatus("SUCCESS");
            } else if (successCount > 0) {
                result.setOverallStatus("PARTIAL_SUCCESS");
            } else {
                result.setOverallStatus("FAILED");
            }

            // 整体AI评估（如果启用且有成功的阶段）
            if (request.getEnableAiEvaluation() && successCount > 0) {
                result = aiEvaluationService.evaluateOverallResult(taskId, result);

                // 打印整体评估结果
                resultPrintService.printOverallResult(result);
            }

        } catch (Exception e) {
            log.error("处理ID失败: id={}, error={}", id, e.getMessage(), e);
            result.setOverallStatus("FAILED");
            result.getErrors().add("处理失败: " + e.getMessage());
        } finally {
            result.setEndTime(LocalDateTime.now());
            if (result.getStartTime() != null && result.getEndTime() != null) {
                result.setTotalDuration(java.time.Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            }

            // 最终保存所有阶段数据到数据库（如果有用户ID且有阶段结果）
            try {
                if (request.getUserId() != null && result.getStageResults() != null && !result.getStageResults().isEmpty()) {
                    for (StageDataDTO stageData : result.getStageResults()) {
                        try {
                            // 检查是否已经保存过（避免重复保存）
                            if (stageData.getStageName() != null) {
                                TDataComparisonStage stage = new TDataComparisonStage();
                                stage.setTaskId(taskId);
                                stage.setDataId(id);
                                stage.setFileMd5(stageData.getFileMd5());
                                stage.setStageName(stageData.getStageName());
                                stage.setDataType(stageData.getDataType());
                                stage.setStageStatus(stageData.getStatus());
                                stage.setErrorMessage(stageData.getErrorMessage());
                                stage.setDuration(stageData.getDuration());
                                stage.setFetchTime(stageData.getFetchTime());
                                stage.setAiEvaluation(stageData.getAiEvaluation());
                                stage.setAiScore(stageData.getAiScore());

                                dataComparisonStageMapper.insert(stage);
                            }
                        } catch (Exception stageException) {
                            log.error("保存阶段数据失败: taskId={}, stage={}, error={}",
                                    taskId, stageData.getStageName(), stageException.getMessage());
                        }
                    }
                    log.info("最终阶段数据已保存到数据库: taskId={}, id={}, stageCount={}",
                            taskId, id, result.getStageResults().size());
                }
            } catch (Exception e) {
                log.error("保存阶段数据到数据库失败: taskId={}, id={}, error={}", taskId, id, e.getMessage(), e);
            }
        }

        // 完成ID处理
        progressManager.completeId(taskId, id, result);

        log.info("完成ID处理: id={}, status={}, successStages={}/{}",
                id, result.getOverallStatus(), successCount, stagesToProcess.size());

        return result;
    }

    @Override
    public String getDefaultUatUrl() {
        return defaultUatUrl;
    }

    @Override
    public String getDefaultTestUrl() {
        return defaultTestUrl;
    }



    /**
     * 计算请求数据的MD5哈希值
     *
     * @param request 请求对象
     * @return MD5哈希值
     */
    private String calculateRequestMd5(DataComparisonRequestDTO request) {
        try {
            // 构建用于计算MD5的字符串
            StringBuilder sb = new StringBuilder();

            // 添加关键请求参数
            if (request.getIds() != null) {
                sb.append("ids:").append(String.join(",", request.getIds()));
            }
            if (request.getUatBaseUrl() != null) {
                sb.append("|uatUrl:").append(request.getUatBaseUrl());
            }
            if (request.getTestBaseUrl() != null) {
                sb.append("|testUrl:").append(request.getTestBaseUrl());
            }
            if (request.getStages() != null) {
                sb.append("|stages:").append(String.join(",", request.getStages()));
            }
            sb.append("|enableAi:").append(request.getEnableAiEvaluation());
            sb.append("|disableChunking:").append(request.getDisableChunking());

            // 计算MD5
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(sb.toString().getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            log.error("计算MD5失败: {}", e.getMessage(), e);
            // 如果MD5计算失败，使用时间戳作为备选方案
            return "fallback_" + System.currentTimeMillis();
        }
    }

    @Override
    public IPage<DataComparisonListDTO> getTaskList(DataComparisonListRequest request) {
        log.info("查询数据对比任务列表: current={}, size={}, status={}, userId={}",
                request.getCurrent(), request.getSize(), request.getOverallStatus(), request.getUserId());

        try {
            // 参数验证
            if (request.getCurrent() == null || request.getCurrent() < 1) {
                request.setCurrent(1);
            }
            if (request.getSize() == null || request.getSize() < 1 || request.getSize() > 100) {
                request.setSize(10);
            }

            // 验证时间范围
            if (!request.isValidTimeRange()) {
                log.warn("时间范围无效: startTime={}, endTime={}", request.getStartTime(), request.getEndTime());
                throw new IllegalArgumentException("开始时间不能晚于结束时间");
            }

            // 验证评分范围
            if (!request.isValidScoreRange()) {
                log.warn("评分范围无效: minScore={}, maxScore={}", request.getMinScore(), request.getMaxScore());
                throw new IllegalArgumentException("最低评分不能高于最高评分");
            }

            // 创建分页对象
            Page<DataComparisonListDTO> page = new Page<>(request.getCurrent(), request.getSize());

            // 执行查询
            IPage<DataComparisonListDTO> result = dataComparisonStageMapper.selectTaskList(request, page);

            log.info("查询完成: 总记录数={}, 当前页={}, 每页大小={}, 总页数={}",
                    result.getTotal(), result.getCurrent(), result.getSize(), result.getPages());

            return result;

        } catch (IllegalArgumentException e) {
            log.error("参数验证失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("查询数据对比任务列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询任务列表失败: " + e.getMessage(), e);
        }
    }

}
