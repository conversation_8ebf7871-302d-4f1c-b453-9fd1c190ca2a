-- 删除t_data_comparison主表的SQL脚本
-- 注意：执行前请确保已备份重要数据

-- 1. 删除外键约束（如果存在）
ALTER TABLE t_data_comparison_stage DROP FOREIGN KEY IF EXISTS t_data_comparison_stage_ibfk_1;

-- 2. 删除主表
DROP TABLE IF EXISTS t_data_comparison;

-- 3. 修改阶段表结构，移除对主表的依赖
-- 删除comparison_id字段，添加task_id字段（如果不存在）
ALTER TABLE t_data_comparison_stage 
DROP COLUMN IF EXISTS comparison_id,
ADD COLUMN IF NOT EXISTS task_id VARCHAR(64) NOT NULL COMMENT '任务ID' AFTER id;

-- 4. 添加新的索引
ALTER TABLE t_data_comparison_stage 
ADD INDEX IF NOT EXISTS idx_task_id (task_id),
ADD INDEX IF NOT EXISTS idx_composite_query_new (task_id, stage_name, stage_status);

-- 5. 删除旧的索引（如果存在）
ALTER TABLE t_data_comparison_stage 
DROP INDEX IF EXISTS idx_comparison_id,
DROP INDEX IF EXISTS idx_composite_query;
