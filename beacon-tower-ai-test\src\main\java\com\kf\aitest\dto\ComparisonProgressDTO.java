package com.kf.aitest.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 对比进度DTO
 */
@Data
public class ComparisonProgressDTO {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 当前处理的ID
     */
    private String currentId;
    
    /**
     * 当前阶段：recognize、extraction、structured、transformer
     */
    private String currentStage;
    
    /**
     * 总体进度百分比（0-100）
     */
    private Double overallProgress;
    
    /**
     * 当前ID的进度百分比（0-100）
     */
    private Double currentIdProgress;
    
    /**
     * 已完成的ID数量
     */
    private Integer completedIds;
    
    /**
     * 总ID数量
     */
    private Integer totalIds;
    
    /**
     * 已完成的阶段数量（当前ID）
     */
    private Integer completedStages;
    
    /**
     * 总阶段数量
     */
    private Integer totalStages = 4;
    
    /**
     * 状态消息
     */
    private String message;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 是否完成
     */
    private Boolean completed = false;
    
    /**
     * 是否有错误
     */
    private Boolean hasError = false;
    
    /**
     * 错误信息
     */
    private String errorMessage;
}
