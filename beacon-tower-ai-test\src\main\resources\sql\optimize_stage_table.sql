-- 优化t_data_comparison_stage表结构
-- 添加唯一约束，优化索引，提升性能

-- 1. 备份现有数据（可选）
-- CREATE TABLE t_data_comparison_stage_backup AS SELECT * FROM t_data_comparison_stage;

-- 2. 优化索引结构
-- 删除可能存在的旧索引
ALTER TABLE t_data_comparison_stage
DROP INDEX IF EXISTS idx_composite_query_new;

-- 添加优化的复合索引
ALTER TABLE t_data_comparison_stage
ADD INDEX IF NOT EXISTS idx_task_data_stage (task_id, data_id, stage_name),
ADD INDEX IF NOT EXISTS idx_status_time (stage_status, create_time),
ADD INDEX IF NOT EXISTS idx_file_md5_time (file_md5, create_time);

-- 3. 添加唯一约束，防止重复数据
ALTER TABLE t_data_comparison_stage
ADD UNIQUE KEY IF NOT EXISTS uk_task_data_stage (task_id, data_id, stage_name);

-- 4. 优化后的表结构说明
/*
优化后的表结构：
- 保留task_id: SSE连接和任务管理的核心标识
- 保留data_id: 具体的数据标识
- 保留file_md5: 请求文件标识，用于去重和缓存
- 保留data_type: 数据类型标识，便于扩展
- 添加了唯一约束防止重复数据
- 优化了索引结构提升查询性能
- 保持与SSE消息推送的兼容性
*/
