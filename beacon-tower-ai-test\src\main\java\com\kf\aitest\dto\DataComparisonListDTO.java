package com.kf.aitest.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据对比任务列表响应DTO
 * 用于前端表格显示的聚合数据
 */
@Data
@Schema(description = "数据对比任务列表响应DTO")
public class DataComparisonListDTO {

    /**
     * 文件MD5哈希值，作为任务的唯一标识
     */
    @Schema(description = "文件MD5哈希值", example = "a1b2c3d4e5f6...")
    private String fileMd5;

    /**
     * 任务ID（用于详情查询和SSE连接）
     */
    @Schema(description = "任务ID", example = "550e8400-e29b-41d4-a716-446655440000")
    private String taskId;

    /**
     * 成功完成的阶段数量
     */
    @Schema(description = "成功完成的阶段数量", example = "3")
    private Integer successStageCount;

    /**
     * 总阶段数量
     */
    @Schema(description = "总阶段数量", example = "4")
    private Integer totalStageCount;

    /**
     * 整体任务状态
     * PENDING: 待执行
     * RUNNING: 执行中  
     * SUCCESS: 全部成功
     * PARTIAL_SUCCESS: 部分成功
     * FAILED: 执行失败
     */
    @Schema(description = "整体任务状态", 
            allowableValues = {"PENDING", "RUNNING", "SUCCESS", "PARTIAL_SUCCESS", "FAILED"},
            example = "SUCCESS")
    private String overallStatus;

    /**
     * 整体AI评分（0-100分）
     * 所有有效AI评分的平均值
     */
    @Schema(description = "整体AI评分", example = "85", minimum = "0", maximum = "100")
    private Integer overallScore;

    /**
     * 任务创建时间
     */
    @Schema(description = "任务创建时间", example = "2025-08-05 11:12:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 任务更新时间
     */
    @Schema(description = "任务更新时间", example = "2025-08-05 11:15:30")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 处理的数据项数量
     */
    @Schema(description = "处理的数据项数量", example = "10")
    private Integer dataItemCount;

    /**
     * 平均处理耗时（毫秒）
     */
    @Schema(description = "平均处理耗时（毫秒）", example = "2500")
    private Long averageDuration;

    /**
     * 错误信息（如果有失败的阶段）
     */
    @Schema(description = "错误信息", example = "部分数据项处理失败")
    private String errorMessage;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "user123")
    private String userId;

    /**
     * 获取当前阶段进度文本
     * 格式：成功数/总数
     */
    @Schema(description = "阶段进度文本", example = "3/4")
    public String getCurrentStageText() {
        if (totalStageCount == null || totalStageCount == 0) {
            return "-";
        }
        return String.format("%d/%d", 
            successStageCount != null ? successStageCount : 0, 
            totalStageCount);
    }

    /**
     * 获取成功率百分比
     */
    @Schema(description = "成功率百分比", example = "75.0")
    public Double getSuccessRate() {
        if (totalStageCount == null || totalStageCount == 0) {
            return 0.0;
        }
        return (successStageCount != null ? successStageCount : 0) * 100.0 / totalStageCount;
    }

    /**
     * 判断任务是否完成（无论成功或失败）
     */
    @Schema(description = "任务是否完成", example = "true")
    public Boolean isCompleted() {
        return "SUCCESS".equals(overallStatus) || 
               "FAILED".equals(overallStatus) || 
               "PARTIAL_SUCCESS".equals(overallStatus);
    }

    /**
     * 判断任务是否正在运行
     */
    @Schema(description = "任务是否正在运行", example = "false")
    public Boolean isRunning() {
        return "RUNNING".equals(overallStatus) || "PENDING".equals(overallStatus);
    }
}
