package com.kf.aitest.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据对比任务列表响应DTO
 * 用于前端表格显示的聚合数据
 */
@Data
public class DataComparisonListDTO {

    /**
     * 文件MD5哈希值，作为任务的唯一标识
     */
    private String fileMd5;

    /**
     * 任务ID（用于详情查询和SSE连接）
     */
    private String taskId;

    /**
     * 成功完成的阶段数量
     */
    private Integer successStageCount;

    /**
     * 总阶段数量
     */
    private Integer totalStageCount;

    /**
     * 整体任务状态
     * PENDING: 待执行
     * RUNNING: 执行中
     * SUCCESS: 全部成功
     * PARTIAL_SUCCESS: 部分成功
     * FAILED: 执行失败
     */
    private String overallStatus;

    /**
     * 整体AI评分（0-100分）
     * 所有有效AI评分的平均值
     */
    private Integer overallScore;

    /**
     * 任务创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 任务更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 处理的数据项数量
     */
    private Integer dataItemCount;

    /**
     * 平均处理耗时（毫秒）
     */
    private Long averageDuration;

    /**
     * 错误信息（如果有失败的阶段）
     */
    private String errorMessage;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 获取当前阶段进度文本
     * 格式：成功数/总数
     */
    public String getCurrentStageText() {
        if (totalStageCount == null || totalStageCount == 0) {
            return "-";
        }
        return String.format("%d/%d",
            successStageCount != null ? successStageCount : 0,
            totalStageCount);
    }

    /**
     * 获取成功率百分比
     */
    public Double getSuccessRate() {
        if (totalStageCount == null || totalStageCount == 0) {
            return 0.0;
        }
        return (successStageCount != null ? successStageCount : 0) * 100.0 / totalStageCount;
    }

    /**
     * 判断任务是否完成（无论成功或失败）
     */
    public Boolean isCompleted() {
        return "SUCCESS".equals(overallStatus) ||
               "FAILED".equals(overallStatus) ||
               "PARTIAL_SUCCESS".equals(overallStatus);
    }

    /**
     * 判断任务是否正在运行
     */
    public Boolean isRunning() {
        return "RUNNING".equals(overallStatus) || "PENDING".equals(overallStatus);
    }
}
