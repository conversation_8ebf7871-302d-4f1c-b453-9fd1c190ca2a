package com.kf.aitest.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kf.aitest.dto.DataComparisonListDTO;
import com.kf.aitest.dto.DataComparisonListRequest;
import com.kf.aitest.dto.DataComparisonRequestDTO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 数据对比服务接口
 */
public interface DataComparisonService {
    
    /**
     * 创建SSE连接
     * 
     * @param taskId 任务ID
     * @return SSE发射器
     */
    SseEmitter createSseConnection(String taskId);
    
    /**
     * 开始数据对比任务
     * 
     * @param taskId 任务ID
     * @param request 对比请求
     */
    void startComparison(String taskId, DataComparisonRequestDTO request);
    
    /**
     * 获取默认的UAT环境URL
     * 
     * @return UAT环境URL
     */
    String getDefaultUatUrl();
    
    /**
     * 获取默认的TEST环境URL
     *
     * @return TEST环境URL
     */
    String getDefaultTestUrl();

    /**
     * 分页查询数据对比任务列表
     *
     * @param request 查询请求参数
     * @return 分页结果
     */
    IPage<DataComparisonListDTO> getTaskList(DataComparisonListRequest request);

}
