-- 数据对比阶段结果表结构
-- 根据TDataComparisonStage实体类生成

-- 数据对比阶段结果表
CREATE TABLE IF NOT EXISTS t_data_comparison_stage (
    -- 主键ID，自增长
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',

    -- 任务标识字段
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID，用于SSE连接和任务管理',
    data_id VARCHAR(64) NOT NULL COMMENT '对比数据ID，标识批量处理中的具体数据项',
    file_md5 VARCHAR(32) COMMENT '请求文件的MD5哈希值，用于去重和缓存',

    -- 阶段信息字段
    stage_name VARCHAR(32) NOT NULL COMMENT '阶段名称：recognize、extraction、structured、transformer',
    data_type VARCHAR(16) COMMENT '数据类型：json、md等',
    stage_status VARCHAR(32) COMMENT '阶段状态：SUCCESS、FAILED、TIMEOUT',
    error_message TEXT COMMENT '错误信息，记录处理失败的详细原因',

    -- 性能和时间字段
    duration BIGINT COMMENT '数据获取耗时(毫秒)，用于性能分析',
    fetch_time DATETIME COMMENT '数据获取时间，记录具体的处理时间点',

    -- AI评估相关字段
    ai_evaluation TEXT COMMENT 'AI评估结果，存储AI分析的详细内容',
    ai_score INT COMMENT 'AI评估得分(0-100)，量化评估结果',

    -- 数据存储字段
    uat_data LONGTEXT COMMENT 'UAT环境数据(JSON格式)，存储原始响应数据',
    test_data LONGTEXT COMMENT 'TEST环境数据(JSON格式)，存储原始响应数据',

    -- 审计字段
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引设计
    INDEX idx_task_id (task_id) COMMENT '任务ID索引，支持按任务查询',
    INDEX idx_data_id (data_id) COMMENT '数据ID索引，支持按数据项查询',
    INDEX idx_file_md5 (file_md5) COMMENT '文件MD5索引，支持去重查询',
    INDEX idx_stage_name (stage_name) COMMENT '阶段名称索引，支持按阶段统计',
    INDEX idx_stage_status (stage_status) COMMENT '阶段状态索引，支持按状态筛选',
    INDEX idx_fetch_time (fetch_time) COMMENT '获取时间索引，支持时间范围查询',
    INDEX idx_ai_score (ai_score) COMMENT 'AI评分索引，支持按评分排序',
    INDEX idx_create_time (create_time) COMMENT '创建时间索引，支持时间排序',

    -- 复合索引
    INDEX idx_task_data (task_id, data_id) COMMENT '任务-数据复合索引，支持任务内数据查询',
    INDEX idx_task_stage (task_id, stage_name) COMMENT '任务-阶段复合索引，支持任务阶段统计',
    INDEX idx_status_time (stage_status, create_time) COMMENT '状态-时间复合索引，支持状态时间分析',
    INDEX idx_composite_query (task_id, data_id, stage_name) COMMENT '三字段复合索引，支持精确查询',

    -- 唯一约束，防止重复数据
    UNIQUE KEY uk_task_data_stage (task_id, data_id, stage_name) COMMENT '防止同一任务的同一数据项的同一阶段重复记录'

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据对比阶段结果表，存储每个数据项在各个处理阶段的详细结果';

-- ==================== 查询示例 ====================

-- 1. 查询特定任务的所有阶段结果
-- SELECT * FROM t_data_comparison_stage
-- WHERE task_id = 'abc-123-456'
-- ORDER BY data_id, stage_name;

-- 2. 查询特定数据项的所有阶段结果
-- SELECT * FROM t_data_comparison_stage
-- WHERE task_id = 'abc-123-456' AND data_id = 'user001'
-- ORDER BY stage_name;

-- 3. 查询任务的处理进度统计
-- SELECT
--     task_id,
--     COUNT(DISTINCT data_id) as total_data_count,
--     COUNT(*) as total_stage_count,
--     SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
--     SUM(CASE WHEN stage_status = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
--     ROUND(SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
-- FROM t_data_comparison_stage
-- WHERE task_id = 'abc-123-456'
-- GROUP BY task_id;

-- 4. 统计各阶段的整体成功率
-- SELECT
--     stage_name,
--     COUNT(*) as total_count,
--     SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
--     SUM(CASE WHEN stage_status = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
--     ROUND(SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate,
--     AVG(duration) as avg_duration_ms,
--     AVG(ai_score) as avg_ai_score
-- FROM t_data_comparison_stage
-- GROUP BY stage_name
-- ORDER BY stage_name;

-- 5. 查询失败的阶段记录
-- SELECT task_id, data_id, stage_name, error_message, create_time
-- FROM t_data_comparison_stage
-- WHERE stage_status = 'FAILED'
-- ORDER BY create_time DESC;

-- 6. 查询AI评分较低的记录
-- SELECT task_id, data_id, stage_name, ai_score, ai_evaluation
-- FROM t_data_comparison_stage
-- WHERE ai_score IS NOT NULL AND ai_score < 60
-- ORDER BY ai_score ASC;

-- 7. 查询处理耗时较长的记录
-- SELECT task_id, data_id, stage_name, duration, fetch_time
-- FROM t_data_comparison_stage
-- WHERE duration IS NOT NULL AND duration > 10000  -- 超过10秒
-- ORDER BY duration DESC;

-- 8. 按文件MD5分组统计
-- SELECT
--     file_md5,
--     COUNT(DISTINCT task_id) as task_count,
--     COUNT(*) as total_records,
--     AVG(ai_score) as avg_ai_score
-- FROM t_data_comparison_stage
-- WHERE file_md5 IS NOT NULL
-- GROUP BY file_md5
-- ORDER BY task_count DESC;

-- 统计各阶段成功率
-- SELECT stage_name,
--        COUNT(*) as total_count,
--        SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
--        ROUND(SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
-- FROM t_data_comparison_stage
-- GROUP BY stage_name;
