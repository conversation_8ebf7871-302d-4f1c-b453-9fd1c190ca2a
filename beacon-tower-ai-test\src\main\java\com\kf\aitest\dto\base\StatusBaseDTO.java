package com.kf.aitest.dto.base;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 状态基础DTO类
 * 包含状态和错误相关的通用字段
 */
@Data
@EqualsAndHashCode(callSuper = true)
public abstract class StatusBaseDTO extends BaseDTO {
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 是否完成
     */
    private Boolean completed = false;
    
    /**
     * 是否有错误
     */
    private Boolean hasError = false;
}
