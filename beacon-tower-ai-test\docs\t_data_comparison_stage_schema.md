# t_data_comparison_stage 表结构说明

## 概述

`t_data_comparison_stage` 表用于存储数据对比任务中每个数据项在各个处理阶段的详细结果。该表支持批量数据处理、实时进度跟踪和AI评估功能。

## 表结构

### 主键字段
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID，自增长 |

### 任务标识字段
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| task_id | VARCHAR(64) | NOT NULL | 任务ID，用于SSE连接和任务管理 |
| data_id | VARCHAR(64) | NOT NULL | 数据ID，标识批量处理中的具体数据项 |
| file_md5 | VARCHAR(32) | NULL | 请求文件的MD5哈希值，用于去重和缓存 |

### 阶段信息字段
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| stage_name | VARCHAR(32) | NOT NULL | 阶段名称：recognize、extraction、structured、transformer |
| data_type | VARCHAR(16) | NULL | 数据类型：json、md、xml等 |
| stage_status | VARCHAR(32) | NULL | 阶段状态：SUCCESS、FAILED、TIMEOUT |
| error_message | TEXT | NULL | 错误信息，记录处理失败的详细原因 |

### 性能和时间字段
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| duration | BIGINT | NULL | 数据获取耗时(毫秒) |
| fetch_time | DATETIME | NULL | 数据获取时间 |

### AI评估相关字段
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| ai_evaluation | TEXT | NULL | AI评估结果，支持Markdown格式 |
| ai_score | INT | NULL | AI评估得分(0-100) |

### 数据存储字段
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| uat_data | LONGTEXT | NULL | UAT环境数据(JSON格式) |
| test_data | LONGTEXT | NULL | TEST环境数据(JSON格式) |

### 审计字段
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| create_time | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

## 索引设计

### 单字段索引
- `idx_task_id`: 支持按任务查询
- `idx_data_id`: 支持按数据项查询
- `idx_file_md5`: 支持去重查询
- `idx_stage_name`: 支持按阶段统计
- `idx_stage_status`: 支持按状态筛选
- `idx_fetch_time`: 支持时间范围查询
- `idx_ai_score`: 支持按评分排序
- `idx_create_time`: 支持时间排序

### 复合索引
- `idx_task_data`: (task_id, data_id) - 查询任务内特定数据项
- `idx_task_stage`: (task_id, stage_name) - 任务阶段统计
- `idx_status_time`: (stage_status, create_time) - 状态时间分析
- `idx_composite_query`: (task_id, data_id, stage_name) - 精确查询

### 唯一约束
- `uk_task_data_stage`: (task_id, data_id, stage_name) - 防止重复记录

## 数据流程

1. **任务创建**: 用户发起请求，生成唯一的task_id
2. **数据处理**: 系统并发处理每个data_id，经过四个阶段
3. **结果存储**: 每个阶段的结果都存储为一条记录
4. **实时推送**: 通过SSE推送处理进度给前端
5. **结果查询**: 支持按任务、数据项、阶段等维度查询

## 使用场景

### 批量数据处理
```sql
-- 查询任务的所有数据项处理结果
SELECT * FROM t_data_comparison_stage 
WHERE task_id = 'abc-123-456' 
ORDER BY data_id, stage_name;
```

### 进度统计
```sql
-- 统计任务处理进度
SELECT 
    COUNT(DISTINCT data_id) as total_data,
    SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count
FROM t_data_comparison_stage 
WHERE task_id = 'abc-123-456';
```

### 性能分析
```sql
-- 分析各阶段平均耗时
SELECT stage_name, AVG(duration) as avg_duration
FROM t_data_comparison_stage 
WHERE duration IS NOT NULL
GROUP BY stage_name;
```

## 注意事项

1. **唯一约束**: 同一任务的同一数据项的同一阶段只能有一条记录
2. **数据量**: 批量处理会产生大量记录，需要定期清理历史数据
3. **索引维护**: 复合索引较多，注意写入性能
4. **JSON数据**: uat_data和test_data字段存储大量JSON数据，注意存储空间
