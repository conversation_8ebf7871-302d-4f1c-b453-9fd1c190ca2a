package com.kf.aitest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * 数据对比任务列表查询请求DTO
 * 支持分页查询和条件筛选
 */
@Data
@Schema(description = "数据对比任务列表查询请求DTO")
public class DataComparisonListRequest {

    /**
     * 当前页码（从1开始）
     */
    @Schema(description = "当前页码", example = "1", minimum = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10", minimum = "1", maximum = "100")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer size = 10;

    /**
     * 用户ID（可选，用于筛选用户的任务）
     */
    @Schema(description = "用户ID", example = "user123")
    private String userId;

    /**
     * 任务ID（可选，精确查询特定任务）
     */
    @Schema(description = "任务ID", example = "550e8400-e29b-41d4-a716-446655440000")
    private String taskId;

    /**
     * 文件MD5（可选，查询特定文件的处理记录）
     */
    @Schema(description = "文件MD5", example = "a1b2c3d4e5f6...")
    private String fileMd5;

    /**
     * 整体状态筛选
     * PENDING: 待执行
     * RUNNING: 执行中
     * SUCCESS: 全部成功
     * PARTIAL_SUCCESS: 部分成功
     * FAILED: 执行失败
     */
    @Schema(description = "整体状态筛选", 
            allowableValues = {"PENDING", "RUNNING", "SUCCESS", "PARTIAL_SUCCESS", "FAILED"},
            example = "SUCCESS")
    private String overallStatus;

    /**
     * 开始时间（可选，时间范围筛选）
     */
    @Schema(description = "开始时间", example = "2025-08-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间（可选，时间范围筛选）
     */
    @Schema(description = "结束时间", example = "2025-08-05 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 最低AI评分筛选（可选）
     */
    @Schema(description = "最低AI评分", example = "60", minimum = "0", maximum = "100")
    @Min(value = 0, message = "AI评分不能小于0")
    @Max(value = 100, message = "AI评分不能大于100")
    private Integer minScore;

    /**
     * 最高AI评分筛选（可选）
     */
    @Schema(description = "最高AI评分", example = "90", minimum = "0", maximum = "100")
    @Min(value = 0, message = "AI评分不能小于0")
    @Max(value = 100, message = "AI评分不能大于100")
    private Integer maxScore;

    /**
     * 排序字段
     * createTime: 按创建时间排序
     * updateTime: 按更新时间排序
     * overallScore: 按整体评分排序
     * successRate: 按成功率排序
     */
    @Schema(description = "排序字段", 
            allowableValues = {"createTime", "updateTime", "overallScore", "successRate"},
            example = "createTime")
    private String orderBy = "createTime";

    /**
     * 排序方向
     * ASC: 升序
     * DESC: 降序
     */
    @Schema(description = "排序方向", allowableValues = {"ASC", "DESC"}, example = "DESC")
    private String orderDirection = "DESC";

    /**
     * 是否只查询已完成的任务
     */
    @Schema(description = "是否只查询已完成的任务", example = "false")
    private Boolean onlyCompleted = false;

    /**
     * 是否只查询有错误的任务
     */
    @Schema(description = "是否只查询有错误的任务", example = "false")
    private Boolean onlyWithErrors = false;

    /**
     * 获取计算后的偏移量（用于数据库查询）
     */
    public Long getOffset() {
        return (long) (current - 1) * size;
    }

    /**
     * 获取限制数量（用于数据库查询）
     */
    public Long getLimit() {
        return (long) size;
    }

    /**
     * 验证时间范围的合理性
     */
    public boolean isValidTimeRange() {
        if (startTime == null || endTime == null) {
            return true;
        }
        return !startTime.isAfter(endTime);
    }

    /**
     * 验证评分范围的合理性
     */
    public boolean isValidScoreRange() {
        if (minScore == null || maxScore == null) {
            return true;
        }
        return minScore <= maxScore;
    }
}
