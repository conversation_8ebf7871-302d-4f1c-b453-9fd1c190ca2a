# 数据对比列表接口测试指南

**创建时间**: 2025-08-05T11:45:00+08:00  
**接口路径**: `GET /api/beacon-tower/ai-test/data-comparison/list`

## 接口概述

该接口用于分页查询数据对比任务列表，将阶段级别的数据聚合为任务级别的统计信息，支持多种筛选条件。

## 请求参数

### 基础分页参数
- `current` (Integer): 当前页码，默认1，最小值1
- `size` (Integer): 每页大小，默认10，范围1-100

### 筛选参数
- `userId` (String): 用户ID筛选
- `taskId` (String): 任务ID精确查询
- `fileMd5` (String): 文件MD5筛选
- `overallStatus` (String): 整体状态筛选
  - `PENDING`: 待执行
  - `RUNNING`: 执行中
  - `SUCCESS`: 全部成功
  - `PARTIAL_SUCCESS`: 部分成功
  - `FAILED`: 执行失败

### 时间范围参数
- `startTime` (DateTime): 开始时间，格式：`yyyy-MM-dd HH:mm:ss`
- `endTime` (DateTime): 结束时间，格式：`yyyy-MM-dd HH:mm:ss`

### 评分范围参数
- `minScore` (Integer): 最低AI评分，范围0-100
- `maxScore` (Integer): 最高AI评分，范围0-100

### 排序参数
- `orderBy` (String): 排序字段，默认`createTime`
  - `createTime`: 按创建时间排序
  - `updateTime`: 按更新时间排序
  - `overallScore`: 按整体评分排序
  - `successRate`: 按成功率排序
- `orderDirection` (String): 排序方向，默认`DESC`
  - `ASC`: 升序
  - `DESC`: 降序

### 其他筛选参数
- `onlyCompleted` (Boolean): 是否只查询已完成的任务，默认false
- `onlyWithErrors` (Boolean): 是否只查询有错误的任务，默认false

## 响应格式

```json
{
  "isSuccess": true,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "fileMd5": "a1b2c3d4e5f6...",
        "taskId": "550e8400-e29b-41d4-a716-446655440000",
        "successStageCount": 3,
        "totalStageCount": 4,
        "overallStatus": "PARTIAL_SUCCESS",
        "overallScore": 85,
        "createTime": "2025-08-05 11:12:00",
        "updateTime": "2025-08-05 11:15:30",
        "dataItemCount": 10,
        "averageDuration": 2500,
        "errorMessage": "部分数据项处理失败",
        "userId": "user123"
      }
    ],
    "total": 1,
    "current": 1,
    "size": 10,
    "pages": 1
  }
}
```

## 测试用例

### 1. 基础分页查询
```bash
curl -X GET "http://localhost:8080/api/beacon-tower/ai-test/data-comparison/list?current=1&size=10" \
  -H "userId: user123" \
  -H "Content-Type: application/json"
```

### 2. 状态筛选查询
```bash
curl -X GET "http://localhost:8080/api/beacon-tower/ai-test/data-comparison/list?current=1&size=10&overallStatus=SUCCESS" \
  -H "userId: user123" \
  -H "Content-Type: application/json"
```

### 3. 时间范围查询
```bash
curl -X GET "http://localhost:8080/api/beacon-tower/ai-test/data-comparison/list?current=1&size=10&startTime=2025-08-01%2000:00:00&endTime=2025-08-05%2023:59:59" \
  -H "userId: user123" \
  -H "Content-Type: application/json"
```

### 4. 评分范围查询
```bash
curl -X GET "http://localhost:8080/api/beacon-tower/ai-test/data-comparison/list?current=1&size=10&minScore=80&maxScore=100" \
  -H "userId: user123" \
  -H "Content-Type: application/json"
```

### 5. 排序查询
```bash
curl -X GET "http://localhost:8080/api/beacon-tower/ai-test/data-comparison/list?current=1&size=10&orderBy=overallScore&orderDirection=DESC" \
  -H "userId: user123" \
  -H "Content-Type: application/json"
```

## 错误处理

### 参数验证错误
```json
{
  "isSuccess": false,
  "message": "参数错误: 开始时间不能晚于结束时间"
}
```

### 系统错误
```json
{
  "isSuccess": false,
  "message": "查询失败: 数据库连接失败"
}
```

## 性能测试

### 测试场景
1. **小数据量**: 100条记录，响应时间应 < 100ms
2. **中等数据量**: 10,000条记录，响应时间应 < 300ms
3. **大数据量**: 100,000条记录，响应时间应 < 500ms

### 测试命令
```bash
# 使用Apache Bench进行性能测试
ab -n 100 -c 10 "http://localhost:8080/api/beacon-tower/ai-test/data-comparison/list?current=1&size=10"
```

## 数据库索引验证

确保以下索引存在以优化查询性能：

```sql
-- 检查索引是否存在
SHOW INDEX FROM t_data_comparison_stage;

-- 关键索引
-- idx_file_md5: 支持按文件MD5分组
-- idx_create_time: 支持时间排序
-- idx_ai_score: 支持评分筛选
-- idx_task_id: 支持任务查询
```

## 前端集成测试

### 1. 验证响应格式兼容性
确保响应数据格式与前端期望的 `AITestTask` 接口匹配。

### 2. 验证分页功能
- 页码切换正常
- 页面大小调整正常
- 总页数计算正确

### 3. 验证筛选功能
- 状态筛选下拉框正常
- 时间范围选择器正常
- 搜索功能正常

### 4. 验证排序功能
- 表头点击排序正常
- 排序方向切换正常

## 监控指标

### 关键指标
- **响应时间**: 平均响应时间应 < 300ms
- **成功率**: 接口成功率应 > 99.9%
- **并发处理**: 支持100并发请求
- **内存使用**: 单次查询内存占用 < 50MB

### 监控命令
```bash
# 查看接口响应时间
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8080/api/beacon-tower/ai-test/data-comparison/list"

# curl-format.txt 内容：
#      time_namelookup:  %{time_namelookup}\n
#         time_connect:  %{time_connect}\n
#      time_appconnect:  %{time_appconnect}\n
#     time_pretransfer:  %{time_pretransfer}\n
#        time_redirect:  %{time_redirect}\n
#   time_starttransfer:  %{time_starttransfer}\n
#                     ----------\n
#           time_total:  %{time_total}\n
```

## 故障排查

### 常见问题
1. **查询超时**: 检查数据库索引和查询条件
2. **内存溢出**: 检查分页大小限制
3. **数据不一致**: 检查聚合查询逻辑
4. **权限问题**: 检查用户ID验证逻辑

### 日志查看
```bash
# 查看应用日志
tail -f logs/beacon-tower-ai-test.log | grep "DataComparisonController"

# 查看数据库慢查询日志
tail -f /var/log/mysql/slow.log
```

## 更新记录

### 2025-08-05T11:55:00+08:00 - 代码简化
**变更内容**: 删除了Swagger相关注解，简化代码结构
**影响**:
- 代码更加简洁，减少了不必要的依赖
- 保持了完整的JavaDoc注释和参数验证
- 接口功能和测试方法保持不变
